#!/bin/bash

# Script to remove custom alias from all users' .bashrc files
# Usage: ./remove_alias.sh [--dry-run]

set -euo pipefail

# Configuration
ALIAS_FILE="alias.txt"
START_MARKER="#####CUSTOMSTART#####"
END_MARKER="#####CUSTOMEND#####"
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--dry-run]"
            echo "  --dry-run    Show which files would be modified without making changes"
            echo "  --help       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if alias.txt exists
if [[ ! -f "$ALIAS_FILE" ]]; then
    echo "Error: $ALIAS_FILE not found in current directory"
    exit 1
fi

# Function to check if a file contains the custom alias block
contains_alias_block() {
    local file="$1"
    grep -q "$START_MARKER" "$file" 2>/dev/null && grep -q "$END_MARKER" "$file" 2>/dev/null
}

# Function to remove alias block from a file
remove_alias_block() {
    local file="$1"
    local temp_file=$(mktemp)
    
    # Use sed to remove lines between markers (inclusive)
    sed "/$START_MARKER/,/$END_MARKER/d" "$file" > "$temp_file"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # Preserve original file permissions and ownership
        cp "$temp_file" "$file"
        echo "  ✓ Removed alias block from $file"
    else
        echo "  → Would remove alias block from $file"
    fi
    
    rm -f "$temp_file"
}

# Function to get all user home directories
get_user_homes() {
    # Get all users with home directories from /etc/passwd
    # Filter out system users (UID < 1000) and users with /bin/false or /usr/sbin/nologin shells
    awk -F: '
        $3 >= 1000 && 
        $7 !~ /\/bin\/false|\/usr\/sbin\/nologin|\/bin\/nologin/ && 
        $6 ~ /^\/home\// 
        { print $6 }
    ' /etc/passwd
    
    # Also include /root if it exists and has a .bashrc
    if [[ -d "/root" && -f "/root/.bashrc" ]]; then
        echo "/root"
    fi
}

# Main execution
echo "Custom Alias Removal Script"
echo "=========================="
echo "Looking for alias block between '$START_MARKER' and '$END_MARKER'"
echo

if [[ "$DRY_RUN" == "true" ]]; then
    echo "🔍 DRY RUN MODE - No files will be modified"
    echo
fi

# Check if running as root for system-wide access
if [[ $EUID -ne 0 ]]; then
    echo "⚠️  Warning: Not running as root. May not have access to all user directories."
    echo "   Consider running with sudo for complete system coverage."
    echo
fi

affected_count=0
total_checked=0

# Process each user's home directory
while IFS= read -r home_dir; do
    bashrc_file="$home_dir/.bashrc"
    
    # Skip if home directory doesn't exist
    if [[ ! -d "$home_dir" ]]; then
        continue
    fi
    
    # Skip if .bashrc doesn't exist
    if [[ ! -f "$bashrc_file" ]]; then
        continue
    fi
    
    # Skip if we can't read the file
    if [[ ! -r "$bashrc_file" ]]; then
        echo "⚠️  Cannot read $bashrc_file (permission denied)"
        continue
    fi
    
    total_checked=$((total_checked + 1))
    
    # Check if the file contains the alias block
    if contains_alias_block "$bashrc_file"; then
        echo "📁 User: $(basename "$home_dir")"
        
        # Show a preview of what will be removed
        echo "  Found alias block in $bashrc_file"
        if [[ "$DRY_RUN" == "true" ]]; then
            echo "  Preview of block to be removed:"
            sed -n "/$START_MARKER/,/$END_MARKER/p" "$bashrc_file" | sed 's/^/    /'
        fi
        
        # Remove the alias block
        if [[ -w "$bashrc_file" ]]; then
            remove_alias_block "$bashrc_file"
            affected_count=$((affected_count + 1))
        else
            echo "  ❌ Cannot write to $bashrc_file (permission denied)"
        fi
        echo
    fi
done < <(get_user_homes)

# Summary
echo "Summary"
echo "======="
echo "Total .bashrc files checked: $total_checked"
echo "Files with alias block found: $affected_count"

if [[ "$DRY_RUN" == "true" ]]; then
    echo
    echo "🔍 This was a dry run. To actually remove the aliases, run:"
    echo "   $0"
else
    echo
    echo "✅ Alias removal complete!"
    if [[ $affected_count -gt 0 ]]; then
        echo "   Users may need to restart their shells or run 'source ~/.bashrc' to see changes."
    fi
fi
