#!/usr/bin/env python3
"""
GitLab Artifact Cleanup Script
Removes artifacts from a GitLab repository that are older than 30 days.

Usage:
    python gitlab_cleanup.py --token YOUR_GITLAB_TOKEN --project PROJECT_ID [--days 30] [--dry-run]

Environment Variables:
    GITLAB_TOKEN: GitLab personal access token (alternative to --token)
    GITLAB_URL: GitLab instance URL (default: https://gitlab.com)

Examples:
    # Delete artifacts older than 30 days
    python gitlab_cleanup.py --token glpat-xxxxx --project 12345

    # Dry run to see what would be deleted
    python gitlab_cleanup.py --token glpat-xxxxx --project 12345 --dry-run

    # Custom days threshold
    python gitlab_cleanup.py --token glpat-xxxxx --project 12345 --days 60

    # Using environment variable for token
    export GITLAB_TOKEN=glpat-xxxxx
    python gitlab_cleanup.py --project 12345
"""

import argparse
import os
import sys
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
import logging
import time

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


def setup_logging(verbose: bool = False) -> logging.Logger:
    """Configure logging for the script."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def create_session() -> requests.Session:
    """Create a requests session with retry logic."""
    session = requests.Session()
    retry = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session


class GitLabArtifactCleaner:
    """Handles GitLab artifact cleanup operations."""
    
    def __init__(self, gitlab_url: str, token: str, project_id: str):
        self.gitlab_url = gitlab_url.rstrip('/')
        self.token = token
        self.project_id = project_id
        self.session = create_session()
        self.headers = {
            'PRIVATE-TOKEN': self.token,
            'Accept': 'application/json'
        }
        self.logger = logging.getLogger(__name__)
        
    def get_jobs_with_artifacts(self) -> List[Dict[str, Any]]:
        """Fetch all jobs with artifacts from the project."""
        jobs = []
        page = 1
        per_page = 100
        total_jobs_checked = 0
        
        while True:
            url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/jobs"
            params = {
                'per_page': per_page,
                'page': page,
                'scope': ['success', 'failed', 'canceled']  # All completed jobs
            }
            
            try:
                self.logger.debug(f"Fetching jobs page {page} from {url}")
                response = self.session.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                
                page_jobs = response.json()
                if not page_jobs:
                    break
                
                total_jobs_checked += len(page_jobs)
                self.logger.debug(f"Page {page}: Found {len(page_jobs)} jobs")
                
                # Log details about jobs for debugging
                for job in page_jobs[:5]:  # Log first 5 jobs for debugging
                    self.logger.debug(
                        f"  Job {job.get('id')}: name={job.get('name')}, "
                        f"has artifacts_file={bool(job.get('artifacts_file'))}, "
                        f"has artifacts={bool(job.get('artifacts'))}"
                    )
                    if job.get('artifacts_file'):
                        self.logger.debug(f"    artifacts_file: {job['artifacts_file']}")
                    if job.get('artifacts'):
                        self.logger.debug(f"    artifacts: {job['artifacts']}")
                    
                # Filter jobs that have artifacts - check multiple fields
                jobs_with_artifacts = [
                    job for job in page_jobs 
                    if (job.get('artifacts_file') and job['artifacts_file'].get('filename')) or
                       (job.get('artifacts') and len(job.get('artifacts', [])) > 0) or
                       job.get('artifacts_expire_at')
                ]
                
                self.logger.debug(f"  Jobs with artifacts on this page: {len(jobs_with_artifacts)}")
                jobs.extend(jobs_with_artifacts)
                
                # Check if there are more pages
                if 'x-next-page' in response.headers and response.headers['x-next-page']:
                    page = int(response.headers['x-next-page'])
                else:
                    break
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Error fetching jobs: {e}")
                self.logger.error(f"Response status: {response.status_code if 'response' in locals() else 'N/A'}")
                self.logger.error(f"Response text: {response.text[:500] if 'response' in locals() else 'N/A'}")
                raise
        
        self.logger.info(f"Checked {total_jobs_checked} total jobs across {page-1} pages")        
        return jobs
    
    def filter_old_artifacts(self, jobs: List[Dict[str, Any]], days_threshold: int) -> List[Dict[str, Any]]:
        """Filter jobs with artifacts older than the specified days threshold."""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_threshold)
        old_jobs = []
        
        self.logger.info(f"Current UTC time: {datetime.now(timezone.utc).isoformat()}")
        self.logger.info(f"Cutoff date ({days_threshold} days ago): {cutoff_date.isoformat()}")
        
        # Log the date range of all jobs
        if jobs:
            dates = []
            for job in jobs:
                if job.get('created_at'):
                    try:
                        created_at = datetime.fromisoformat(job['created_at'].replace('Z', '+00:00'))
                        dates.append(created_at)
                    except:
                        pass
            
            if dates:
                dates.sort()
                self.logger.info(f"Jobs date range: {dates[0].isoformat()} to {dates[-1].isoformat()}")
                self.logger.info(f"Newest job: {(datetime.now(timezone.utc) - dates[-1]).days} days ago")
                self.logger.info(f"Oldest job: {(datetime.now(timezone.utc) - dates[0]).days} days ago")
        
        for idx, job in enumerate(jobs):
            # Parse the created_at timestamp
            created_at_str = job.get('created_at', '')
            if not created_at_str:
                self.logger.debug(f"Job {job.get('id')} has no created_at field")
                continue
                
            try:
                # GitLab returns ISO format timestamps
                created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                days_old = (datetime.now(timezone.utc) - created_at).days
                
                # Log first few jobs to see their dates
                if idx < 5:
                    self.logger.debug(
                        f"Job {job['id']} ({job['name']}): created {created_at_str} "
                        f"({days_old} days ago) - {'OLD' if created_at < cutoff_date else 'RECENT'}"
                    )
                
                if created_at < cutoff_date:
                    old_jobs.append(job)
                    self.logger.debug(
                        f"Job {job['id']} ({job['name']}) from {created_at_str} "
                        f"is older than {days_threshold} days ({days_old} days old)"
                    )
            except (ValueError, TypeError) as e:
                self.logger.warning(f"Could not parse date for job {job.get('id')}: {e}")
        
        self.logger.info(f"Filtered: {len(old_jobs)} of {len(jobs)} jobs are older than {days_threshold} days")        
        return old_jobs
    
    def delete_job_artifacts(self, job_id: int, job_name: str = None) -> bool:
        """Delete artifacts for a specific job."""
        url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/jobs/{job_id}/artifacts"
        
        try:
            response = self.session.delete(url, headers=self.headers)
            
            if response.status_code == 204:
                self.logger.info(f"✓ Deleted artifacts for job {job_id} ({job_name or 'unknown'})")
                return True
            elif response.status_code == 404:
                self.logger.warning(f"⚠ No artifacts found for job {job_id} ({job_name or 'unknown'})")
                return False
            else:
                self.logger.error(
                    f"✗ Failed to delete artifacts for job {job_id}: "
                    f"HTTP {response.status_code} - {response.text}"
                )
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"✗ Error deleting artifacts for job {job_id}: {e}")
            return False
    
    def cleanup_artifacts(self, days_threshold: int = 30, dry_run: bool = False) -> Dict[str, Any]:
        """Main cleanup function to remove old artifacts."""
        self.logger.info(f"Starting GitLab artifact cleanup for project {self.project_id}")
        self.logger.info(f"Threshold: {days_threshold} days")
        
        if dry_run:
            self.logger.info("DRY RUN MODE - No artifacts will be deleted")
        
        # Get all jobs with artifacts
        self.logger.info("Fetching jobs with artifacts...")
        jobs = self.get_jobs_with_artifacts()
        self.logger.info(f"Found {len(jobs)} jobs with artifacts")
        
        # Filter old artifacts
        old_jobs = self.filter_old_artifacts(jobs, days_threshold)
        self.logger.info(f"Found {len(old_jobs)} jobs with artifacts older than {days_threshold} days")
        
        # Calculate total size to be freed
        total_size = sum(
            job.get('artifacts_file', {}).get('size', 0) 
            for job in old_jobs
        )
        total_size_mb = total_size / (1024 * 1024)
        
        if not old_jobs:
            self.logger.info("No old artifacts to delete")
            return {
                'total_jobs': len(jobs),
                'deleted_count': 0,
                'failed_count': 0,
                'space_freed_mb': 0
            }
        
        self.logger.info(f"Estimated space to free: {total_size_mb:.2f} MB")
        
        # Delete artifacts
        deleted_count = 0
        failed_count = 0
        
        for job in old_jobs:
            job_id = job['id']
            job_name = job.get('name', 'unknown')
            created_at = job.get('created_at', 'unknown')
            size_mb = job.get('artifacts_file', {}).get('size', 0) / (1024 * 1024)
            
            self.logger.info(
                f"Processing job {job_id} ({job_name}) - "
                f"Created: {created_at}, Size: {size_mb:.2f} MB"
            )
            
            if not dry_run:
                if self.delete_job_artifacts(job_id, job_name):
                    deleted_count += 1
                else:
                    failed_count += 1
                    
                # Small delay to avoid rate limiting
                time.sleep(0.1)
            else:
                self.logger.info(f"  [DRY RUN] Would delete artifacts for job {job_id}")
                deleted_count += 1
        
        # Summary
        actual_space_freed = sum(
            job.get('artifacts_file', {}).get('size', 0) 
            for job in old_jobs[:deleted_count]
        ) / (1024 * 1024)
        
        results = {
            'total_jobs': len(jobs),
            'old_jobs': len(old_jobs),
            'deleted_count': deleted_count,
            'failed_count': failed_count,
            'space_freed_mb': actual_space_freed if not dry_run else total_size_mb,
            'dry_run': dry_run
        }
        
        self.logger.info("\n" + "="*50)
        self.logger.info("CLEANUP SUMMARY")
        self.logger.info("="*50)
        self.logger.info(f"Total jobs with artifacts: {results['total_jobs']}")
        self.logger.info(f"Jobs older than {days_threshold} days: {results['old_jobs']}")
        self.logger.info(f"Artifacts deleted: {results['deleted_count']}")
        if failed_count > 0:
            self.logger.warning(f"Failed deletions: {results['failed_count']}")
        self.logger.info(f"Space freed: {results['space_freed_mb']:.2f} MB")
        if dry_run:
            self.logger.info("(DRY RUN - no actual changes made)")
        
        return results


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description='Clean up old GitLab artifacts',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--token',
        help='GitLab personal access token (or set GITLAB_TOKEN env var)',
        default=os.environ.get('GITLAB_TOKEN')
    )
    
    parser.add_argument(
        '--project',
        required=True,
        help='GitLab project ID or path (e.g., 12345 or group/project)'
    )
    
    parser.add_argument(
        '--gitlab-url',
        default=os.environ.get('GITLAB_URL', 'https://gitlab.com'),
        help='GitLab instance URL (default: https://gitlab.com)'
    )
    
    parser.add_argument(
        '--days',
        type=int,
        default=30,
        help='Delete artifacts older than this many days (default: 30)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be deleted without actually deleting'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Validate token
    if not args.token:
        print("Error: GitLab token is required. Use --token or set GITLAB_TOKEN environment variable.")
        sys.exit(1)
    
    # Setup logging
    logger = setup_logging(args.verbose)
    
    try:
        # Create cleaner instance
        cleaner = GitLabArtifactCleaner(
            gitlab_url=args.gitlab_url,
            token=args.token,
            project_id=args.project
        )
        
        # Run cleanup
        results = cleaner.cleanup_artifacts(
            days_threshold=args.days,
            dry_run=args.dry_run
        )
        
        # Exit with appropriate code
        if results['failed_count'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()